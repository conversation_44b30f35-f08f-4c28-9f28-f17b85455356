absl-py==2.1.0
accelerate==1.3.0
aenum==3.1.15
aiohappyeyeballs==2.4.6
aiohttp==3.11.12
aiohttp-cors==0.7.0
aiosignal==1.3.2
airportsdata==20241001
annotated-types==0.7.0
antlr4-python3-runtime==4.13.2
anyio==4.8.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
astor==0.8.1
asttokens==3.0.0
async-lru==2.0.4
attrs==25.1.0
av==14.1.0
babel==2.17.0
beautifulsoup4==4.13.3
bitsandbytes==0.45.2
black==25.1.0
blake3==1.0.4
bleach==6.2.0
blis==0.7.11
cachetools==5.5.1
catalogue==2.0.10
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
click==8.1.8
clip @ git+https://github.com/openai/CLIP.git@dcba3cb2e2827b402d2701e7e1c7d9fed8a20ef1
cloudpathlib==0.16.0
cloudpickle==3.1.1
colorama==0.4.6
colorful==0.5.6
colorlog==6.9.0
comm==0.2.2
compressed-tensors==0.9.1
confection==0.1.5
contourpy==1.3.1
cycler==0.12.1
cymem==2.0.11
DataProperty==1.1.0
datasets==3.2.0
debugpy==1.8.12
decorator==5.1.1
deepspeed==0.15.4
defusedxml==0.7.1
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
docker-pycreds==0.4.0
einops==0.8.1
executing==2.2.0
fastapi==0.115.8
fastjsonschema==2.21.1
filelock==3.17.0
flake8==7.1.1
flash_attn==2.7.4.post1
fonttools==4.56.0
fqdn==1.5.1
frozenlist==1.5.0
fsspec==2024.9.0
ftfy==6.3.1
gguf==0.10.0
gitdb==4.0.12
GitPython==3.1.44
google-api-core==2.24.1
google-auth==2.38.0
googleapis-common-protos==1.67.0
grpcio==1.70.0
h11==0.14.0
hf_transfer==0.1.9
hjson==3.1.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.28.1
idna==3.10
importlib_metadata==8.6.1
iniconfig==2.0.0
inquirerpy==0.3.4
interegular==0.3.3
ipykernel==6.29.5
ipython==8.32.0
ipywidgets==8.1.5
isoduration==20.11.0
isort==6.0.0
jedi==0.19.2
Jinja2==3.1.5
jiter==0.8.2
joblib==1.4.2
json5==0.10.0
jsonlines==4.0.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyterlab==4.3.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
kiwisolver==1.4.8
langcodes==3.5.0
language_data==1.3.0
lark==1.2.2
latex2sympy2_extended==1.0.6
liger_kernel==0.5.2
lighteval @ git+https://github.com/huggingface/lighteval.git@4f381b352c0e467b5870a97d41cb66b487a2c503
lm-format-enforcer==0.10.9
lxml==5.3.1
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
math-verify==0.5.2
matplotlib==3.10.0
matplotlib-inline==0.1.7
mbstrdecoder==1.1.4
mccabe==0.7.0
mdurl==0.1.2
mistral_common==1.5.3
mistune==3.1.1
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
multiprocess==0.70.16
murmurhash==1.0.12
mypy-extensions==1.0.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
ninja==********
nltk==3.9.1
notebook==7.3.2
notebook_shim==0.2.4
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-ml-py==12.570.86
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
openai==1.63.0
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python==*********
opencv-python-headless==*********
outlines==0.1.11
outlines_core==0.1.26
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
parameterized==0.9.0
parso==0.8.4
partial-json-parser==*******.post5
pathspec==0.12.1
pathvalidate==3.2.3
pexpect==4.9.0
pfzy==0.3.4
pillow==11.1.0
platformdirs==4.3.6
pluggy==1.5.0
portalocker==3.1.1
preshed==3.0.9
prometheus-fastapi-instrumentator==7.0.2
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.26.0
protobuf==3.20.3
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
py-spy==0.4.0
pyarrow==19.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pybind11==2.13.6
pycodestyle==2.12.1
pycountry==24.6.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pyflakes==3.2.0
Pygments==2.19.1
pyparsing==3.2.1
pytablewriter==1.2.1
pytest==8.3.4
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==3.2.1
pytz==2025.1
PyYAML==6.0.2
pyzmq==26.2.1
qwen-vl-utils==0.0.10
-e git+ssh://**************/Deep-Agent/R1-V.git@b97379a10f59b12d3b5d9e63f478aa98ebf97b27#egg=r1_v&subdirectory=src/r1-v
ray==2.42.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.4
rouge_score==0.1.2
rpds-py==0.22.3
rsa==4.9
sacrebleu==2.5.1
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.1
Send2Trash==1.8.3
sentencepiece==0.2.0
sentry-sdk==2.21.0
setproctitle==1.3.4
six==1.17.0
smart-open==6.4.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
spacy==3.7.2
spacy-legacy==3.0.12
spacy-loggers==1.0.5
srsly==2.5.1
stack-data==0.6.3
starlette==0.45.3
sympy==1.13.1
tabledata==1.3.4
tabulate==0.9.0
tcolorpy==0.1.7
tensorboardX==*******
termcolor==2.3.0
terminado==0.18.1
thinc==8.2.5
threadpoolctl==3.5.0
tiktoken==0.8.0
tinycss2==1.4.0
tokenizers==0.21.0
torch==2.5.1
torchaudio==2.5.1
torchvision==0.20.1
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers @ git+https://github.com/huggingface/transformers.git@92c5ca9dd70de3ade2af2eb835c96215cc50e815
triton==3.1.0
trl==0.14.0
typepy==1.3.4
typer==0.9.4
types-python-dateutil==2.9.0.20241206
typing_extensions==4.12.2
tzdata==2025.1
uri-template==1.3.0
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
virtualenv==20.29.2
vllm==0.7.2
wandb==0.18.3
wasabi==1.1.3
watchfiles==1.0.4
wcwidth==0.2.13
weasel==0.3.4
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==14.2
widgetsnbextension==4.0.13
xformers==0.0.28.post3
xgrammar==0.1.11
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
