#!/bin/bash

# ECG-Chat GRPO Training Script
# This script runs GRPO training for ECG-Chat multimodal model

# Activate the conda environment
source activate /data/yuanxiaoyan/ECGpro/llava

# Change to the correct directory
cd /data/yuanxiaoyan/workspace/R1-V-main/src/r1-v

# Add the current directory to Python path
export PYTHONPATH="/data/yuanxiaoyan/workspace/R1-V-main/src/r1-v:/data/yuanxiaoyan/workspace/R1-V-main/src/r1-v/src:$PYTHONPATH"

# Set environment variables for debugging (optional)
export DEBUG_MODE="true"
export LOG_PATH="./debug_log_ecg_grpo.txt"

# Model and data paths - MODIFY THESE PATHS ACCORDING TO YOUR SETUP
MODEL_PATH="/data/yuanxiaoyan/ECGpro/ECG-Chat/checkpoints/llava-vicuna-13b-v1.5-finetune_lora"  # Path to merged ECG-Chat model (LoRA + base)
ECG_TOWER_PATH="/data/yuanxiaoyan/ECGpro/Pertrain_M/cpt_wfep_epoch_20.pt"          # Path to ECG tower model
ECG_FOLDER="/data/yuanxiaoyan/ECGpro/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0"               # Root folder containing ECG data files
OPEN_CLIP_CONFIG="ecg_config"                # OpenCLIP configuration name
DATASET_NAME="/data/yuanxiaoyan/ECGpro/ECG-Chat/llava/playground/data/diagnosis.json"     # Path to ECG GRPO dataset
OUTPUT_DIR=".checkpoints/ecg-grpo"        # Output directory for trained model

# Training hyperparameters
NUM_GENERATIONS=4                            # Number of generations per prompt
MAX_PROMPT_LENGTH=512                        # Maximum prompt length
MAX_COMPLETION_LENGTH=256                    # Maximum completion length
TEMPERATURE=1.0                              # Sampling temperature
BETA=0.1                                     # GRPO beta parameter
LEARNING_RATE=1e-6                          # Learning rate
BATCH_SIZE=1                                 # Per device batch size
GRADIENT_ACCUMULATION_STEPS=8                # Gradient accumulation steps
NUM_EPOCHS=2                                 # Number of training epochs
SAVE_STEPS=100                               # Save model every N steps
LOGGING_STEPS=1                              # Log every N steps

# Reward functions for ECG tasks
REWARD_FUNCS="ecg_accuracy,ecg_format"       # ECG-specific reward functions

# Run GRPO training
torchrun --nproc_per_node="1" \
    --nnodes="1" \
    --node_rank="0" \
    --master_addr="127.0.0.1" \
    --master_port="12345" \
    src/open_r1/grpo.py \
    --modality ecg \
    --output_dir ${OUTPUT_DIR} \
    --model_name_or_path ${MODEL_PATH} \
    --dataset_name ${DATASET_NAME} \
    --ecg_tower_path ${ECG_TOWER_PATH} \
    --ecg_folder ${ECG_FOLDER} \
    --open_clip_config ${OPEN_CLIP_CONFIG} \
    --seq_length 5000 \
    --reward_funcs ${REWARD_FUNCS} \
    --num_generations ${NUM_GENERATIONS} \
    --max_prompt_length ${MAX_PROMPT_LENGTH} \
    --max_completion_length ${MAX_COMPLETION_LENGTH} \
    --temperature ${TEMPERATURE} \
    --beta ${BETA} \
    --learning_rate ${LEARNING_RATE} \
    --per_device_train_batch_size ${BATCH_SIZE} \
    --gradient_accumulation_steps ${GRADIENT_ACCUMULATION_STEPS} \
    --num_train_epochs ${NUM_EPOCHS} \
    --save_steps ${SAVE_STEPS} \
    --logging_steps ${LOGGING_STEPS} \
    --bf16 \
    --gradient_checkpointing true \
    --attn_implementation flash_attention_2 \
    --report_to wandb \
    --run_name "ECG-Chat-GRPO-$(date +%Y%m%d-%H%M%S)" \
    --save_only_model true \
    --dataloader_num_workers 4 \
    --remove_unused_columns false \
    --eval_strategy "no" \
    --warmup_ratio 0.03 \
    --lr_scheduler_type "cosine" \
    --weight_decay 0.01

echo "ECG-Chat GRPO training completed!"
echo "Model saved to: ${OUTPUT_DIR}"
