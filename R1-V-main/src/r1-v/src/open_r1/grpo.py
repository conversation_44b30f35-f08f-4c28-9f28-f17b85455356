# Copyright 2025 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import re
from datetime import datetime
from dataclasses import dataclass, field
from typing import Optional

from datasets import load_dataset, load_from_disk
from transformers import Qwen2VLForConditionalGeneration

from math_verify import parse, verify
from open_r1.trainer import Qwen2V<PERSON>GRPOTrainer, Qwen2VLGRPOVLLMTrainer, Qwen2VLGRPOVLLMTrainerModified, ECGChatGRPOTrainer
from trl import GRPOConfig, GRPOTrainer, ModelConfig, ScriptArguments, Trl<PERSON><PERSON>er, get_peft_config


@dataclass
class GRPOScriptArguments(ScriptArguments):
    """
    Script arguments for the GRPO training script.

    Args:
        reward_funcs (`list[str]`):
            List of reward functions. Possible values: 'accuracy', 'format'.
    """

    reward_funcs: list[str] = field(
        default_factory=lambda: ["accuracy", "format"],
        metadata={"help": "List of reward functions. Possible values: 'accuracy', 'format'"},
    )
    max_pixels: Optional[int] = field(
        default=12845056,
        metadata={"help": "Maximum number of pixels for the image"},
    )
    min_pixels: Optional[int] = field(
        default=3136,
        metadata={"help": "Minimum number of pixels for the image"},
    )
    modality: Optional[str] = field(
        default="vision",
        metadata={"help": "Modality type: 'vision' for Qwen2-VL, 'ecg' for ECG-Chat"},
    )
    ecg_tower_path: Optional[str] = field(
        default=None,
        metadata={"help": "Path to ECG tower model"},
    )
    ecg_folder: Optional[str] = field(
        default=None,
        metadata={"help": "Root folder containing ECG data files"},
    )
    open_clip_config: Optional[str] = field(
        default=None,
        metadata={"help": "OpenCLIP configuration for ECG encoder"},
    )
    seq_length: Optional[int] = field(
        default=5000,
        metadata={"help": "ECG sequence length for padding/truncation"},
    )


def accuracy_reward(completions, solution, **kwargs):
    """Reward function that checks if the completion is correct using either symbolic verification or exact string matching."""
    contents = [completion[0]["content"] for completion in completions]
    rewards = []
    current_time = datetime.now().strftime("%d-%H-%M-%S-%f")
    for content, sol in zip(contents, solution):
        reward = 0.0
        # Try symbolic verification first
        try:
            answer = parse(content)
            if float(verify(answer, parse(sol))) > 0:
                reward = 1.0
        except Exception:
            pass  # Continue to next verification method if this fails

        # If symbolic verification failed, try string matching
        if reward == 0.0:
            try:
                # Extract answer from solution if it has think/answer tags
                sol_match = re.search(r'<answer>(.*?)</answer>', sol)
                ground_truth = sol_match.group(1).strip() if sol_match else sol.strip()
                
                # Extract answer from content if it has think/answer tags
                content_match = re.search(r'<answer>(.*?)</answer>', content)
                student_answer = content_match.group(1).strip() if content_match else content.strip()
                
                # Compare the extracted answers
                if student_answer == ground_truth:
                    reward = 1.0
            except Exception:
                pass  # Keep reward as 0.0 if both methods fail
                
        rewards.append(reward)
        if os.getenv("DEBUG_MODE") == "true":
            log_path = os.getenv("LOG_PATH")
            # local_rank = int(os.getenv("LOCAL_RANK", 0))
            with open(log_path, "a") as f:
                f.write(f"------------- {current_time} Accuracy reward: {reward} -------------\n")
                f.write(f"Content: {content}\n")
                f.write(f"Solution: {sol}\n")
    return rewards


def format_reward(completions, **kwargs):
    """Reward function that checks if the completion has a specific format."""
    pattern = r"<think>.*?</think>\s*<answer>.*?</answer>"
    completion_contents = [completion[0]["content"] for completion in completions]
    matches = [re.fullmatch(pattern, content, re.DOTALL) for content in completion_contents]
    return [1.0 if match else 0.0 for match in matches]


def ecg_accuracy_reward(completions, solution, **kwargs):
    """ECG-specific reward function that checks medical accuracy."""
    contents = [completion[0]["content"] for completion in completions]
    rewards = []

    for content, sol in zip(contents, solution):
        reward = 0.0
        try:
            # Simple string matching for ECG medical terms
            content_lower = content.lower()
            sol_lower = sol.lower()

            # Check for exact match first
            if content_lower.strip() == sol_lower.strip():
                reward = 1.0
            else:
                # Check for key medical terms overlap
                medical_terms = ['normal', 'abnormal', 'arrhythmia', 'atrial fibrillation',
                               'ventricular', 'tachycardia', 'bradycardia', 'ischemia',
                               'infarction', 'st elevation', 'st depression']

                content_terms = set([term for term in medical_terms if term in content_lower])
                sol_terms = set([term for term in medical_terms if term in sol_lower])

                if content_terms and sol_terms:
                    # Jaccard similarity for medical terms
                    intersection = len(content_terms.intersection(sol_terms))
                    union = len(content_terms.union(sol_terms))
                    reward = intersection / union if union > 0 else 0.0

        except Exception:
            pass  # Keep reward as 0.0 if processing fails

        rewards.append(reward)

    return rewards


def ecg_format_reward(completions, **kwargs):
    """ECG-specific format reward function."""
    completion_contents = [completion[0]["content"] for completion in completions]
    rewards = []

    for content in completion_contents:
        reward = 0.0
        # Check if response contains medical reasoning structure
        if any(keyword in content.lower() for keyword in ['analysis', 'findings', 'conclusion', 'diagnosis']):
            reward += 0.5

        # Check if response is not too short or too long
        word_count = len(content.split())
        if 10 <= word_count <= 200:
            reward += 0.5

        rewards.append(reward)

    return rewards


reward_funcs_registry = {
    "accuracy": accuracy_reward,
    "format": format_reward,
    "ecg_accuracy": ecg_accuracy_reward,
    "ecg_format": ecg_format_reward,
}

SYSTEM_PROMPT = (
    "A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant "
    "first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning "
    "process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., "
    "<think> reasoning process here </think><answer> answer here </answer>"
)


def main(script_args, training_args, model_args):
    # Get reward functions
    reward_funcs = [reward_funcs_registry[func] for func in script_args.reward_funcs]

    # Load the dataset
    dataset = load_dataset(script_args.dataset_name, name=script_args.dataset_config)


    # Format into conversation
    def make_conversation(example):
        return {
            "prompt": [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": example["problem"]},
            ],
        }

    def make_conversation_ecg(example):
        """Format ECG data into conversation format"""
        # ECG prompt should start with <ecg> token
        question = example.get("question", example.get("problem", ""))
        ecg_prompt = f"<ecg>\n{question}"

        return {
            "prompt": [
                {"role": "user", "content": ecg_prompt},
            ],
            "ecg_path": example.get("ecg_path", example.get("ecg", "")),
        }

    # def make_conversation_image(example):
    #     return {
    #         "prompt": [
    #             {"role": "system", "content": [{"type": "text", "text": SYSTEM_PROMPT}]},
    #             {
    #                 "role": "user",
    #                 "content": [
    #                     {"type": "image"},
    #                     {"type": "text", "text": example["problem"]},
    #                 ],
    #             },
    #         ],
    #     }

    QUESTION_TEMPLATE = "{Question}  Output the thinking process in <think> </think> and final answer (number) in <answer> </answer> tags."

    def make_conversation_image(example):
        return {
            "prompt": [
                {
                    "role": "user",
                    "content": [
                        {"type": "image"},
                        {"type": "text", "text": QUESTION_TEMPLATE.format(Question=example["problem"])},
                    ],
                },
            ],
        }


    # Choose data processing based on modality
    if script_args.modality == "ecg":
        print("Processing ECG dataset")
        dataset = dataset.map(make_conversation_ecg)
        # Remove unnecessary columns but keep ecg_path
        columns_to_remove = [col for col in dataset[script_args.dataset_train_split].column_names
                           if col not in ["prompt", "ecg_path", "solution"]]
        if columns_to_remove:
            dataset = dataset.remove_columns(columns_to_remove)
    elif "image" in dataset[script_args.dataset_train_split].features:
        print("has image in dataset")
        dataset = dataset.map(make_conversation_image)
    else:
        print("no image in dataset")
        dataset = dataset.map(make_conversation)
        dataset = dataset.remove_columns("messages")

    # Choose trainer based on modality and vLLM usage
    if script_args.modality == "ecg":
        trainer_cls = ECGChatGRPOTrainer
        print("using ECG-Chat GRPO trainer: ", trainer_cls)

        # Initialize ECG-specific trainer
        trainer = trainer_cls(
            model=model_args.model_name_or_path,
            reward_funcs=reward_funcs,
            args=training_args,
            train_dataset=dataset[script_args.dataset_train_split],
            eval_dataset=dataset[script_args.dataset_test_split] if training_args.eval_strategy != "no" else None,
            peft_config=get_peft_config(model_args),
            attn_implementation=model_args.attn_implementation,
            ecg_tower_path=script_args.ecg_tower_path,
            ecg_folder=script_args.ecg_folder,
            open_clip_config=script_args.open_clip_config,
            seq_length=script_args.seq_length,
        )
    else:
        trainer_cls = Qwen2VLGRPOTrainer if not training_args.use_vllm else Qwen2VLGRPOVLLMTrainerModified
        print("using vision GRPO trainer: ", trainer_cls)

        # Initialize vision trainer
        trainer = trainer_cls(
            model=model_args.model_name_or_path,
            reward_funcs=reward_funcs,
            args=training_args,
            train_dataset=dataset[script_args.dataset_train_split],
            eval_dataset=dataset[script_args.dataset_test_split] if training_args.eval_strategy != "no" else None,
            peft_config=get_peft_config(model_args),
            attn_implementation=model_args.attn_implementation,
            max_pixels=script_args.max_pixels,
            min_pixels=script_args.min_pixels,
        )

    # Train and push the model to the Hub
    trainer.train()

    # Save and push to hub
    trainer.save_model(training_args.output_dir)
    if training_args.push_to_hub:
        trainer.push_to_hub(dataset_name=script_args.dataset_name)


if __name__ == "__main__":
    parser = TrlParser((GRPOScriptArguments, GRPOConfig, ModelConfig))
    script_args, training_args, model_args = parser.parse_args_and_config()
    main(script_args, training_args, model_args)
