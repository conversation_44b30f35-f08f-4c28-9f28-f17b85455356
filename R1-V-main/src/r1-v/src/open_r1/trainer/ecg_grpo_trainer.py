# Copyright 2025 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import sys
import textwrap
import numpy as np
import wfdb
from collections import defaultdict
from typing import Any, Callable, Optional, Union
from pathlib import Path

import torch
import torch.utils.data
import transformers
from datasets import Dataset, IterableDataset
from packaging import version
from transformers import (
    AutoModelForCausalLM,
    AutoModelForSequenceClassification,
    AutoTokenizer,
    GenerationConfig,
    PreTrainedModel,
    PreTrainedTokenizerBase,
    Trainer,
    TrainerCallback,
    is_wandb_available,
)
from transformers.integrations.deepspeed import is_deepspeed_zero3_enabled
from transformers.utils import is_peft_available

from trl.data_utils import apply_chat_template, is_conversational, maybe_apply_chat_template
from trl.models import create_reference_model, prepare_deepspeed, unwrap_model_for_generation
from trl.trainer.grpo_config import GRPOConfig
from trl.trainer.utils import generate_model_card, get_comet_experiment_url

import copy

# Add ECG-Chat specific imports
sys.path.append('/data/yuanxiaoyan/workspace/R1-V-main/llava')
sys.path.append('/data/yuanxiaoyan/workspace/R1-V-main/open_clip')

from llava.model.builder import load_pretrained_model
from llava.constants import ECG_TOKEN_INDEX, DEFAULT_ECG_TOKEN
from llava.mm_utils import tokenizer_ecg_token

if is_peft_available():
    from peft import PeftConfig, get_peft_model, PeftModel

if is_wandb_available():
    import wandb

# What we call a reward function is a callable that takes a list of prompts and completions and returns a list of
# rewards. When it's a string, it's a model ID, so it's loaded as a pretrained model.
RewardFunc = Union[str, PreTrainedModel, Callable[[list, list], list[float]]]


class ECGChatGRPOTrainer(Trainer):
    """
    ECG-Chat specialized GRPO Trainer for ECG multimodal reasoning tasks.
    
    This trainer adapts the Group Relative Policy Optimization (GRPO) method for ECG-Chat models,
    handling ECG signal processing, tokenization, and multimodal forward passes.
    """

    def __init__(
        self,
        model: Union[str, PreTrainedModel],
        reward_funcs: Union[RewardFunc, list[RewardFunc]],
        args: GRPOConfig = None,
        train_dataset: Optional[Union[Dataset, IterableDataset]] = None,
        eval_dataset: Optional[Union[Dataset, IterableDataset, dict[str, Union[Dataset, IterableDataset]]]] = None,
        processing_class: Optional[PreTrainedTokenizerBase] = None,
        reward_processing_classes: Optional[Union[PreTrainedTokenizerBase, list[PreTrainedTokenizerBase]]] = None,
        callbacks: Optional[list[TrainerCallback]] = None,
        optimizers: tuple[Optional[torch.optim.Optimizer], Optional[torch.optim.lr_scheduler.LambdaLR]] = (None, None),
        peft_config: Optional["PeftConfig"] = None,
        ecg_tower_path: Optional[str] = None,
        ecg_folder: Optional[str] = None,
        open_clip_config: Optional[str] = None,
        seq_length: int = 5000,
        attn_implementation: str = "flash_attention_2",
    ):
        # Args
        if args is None:
            model_name = model if isinstance(model, str) else model.config._name_or_path
            model_name = model_name.split("/")[-1]
            args = GRPOConfig(f"{model_name}-ECG-GRPO")

        # Store ECG-specific parameters
        self.ecg_tower_path = ecg_tower_path
        self.ecg_folder = ecg_folder
        self.open_clip_config = open_clip_config
        self.seq_length = seq_length

        # Models
        # Trained model
        model_init_kwargs = args.model_init_kwargs or {}
        model_init_kwargs["attn_implementation"] = attn_implementation
        
        if isinstance(model, str):
            model_id = model
            torch_dtype = model_init_kwargs.get("torch_dtype")
            if isinstance(torch_dtype, torch.dtype) or torch_dtype == "auto" or torch_dtype is None:
                pass  # torch_dtype is already a torch.dtype or "auto" or None
            elif isinstance(torch_dtype, str):  # it's a str, but not "auto"
                torch_dtype = getattr(torch, torch_dtype)
                model_init_kwargs["torch_dtype"] = torch_dtype
            else:
                raise ValueError(
                    "Invalid `torch_dtype` passed to `GRPOConfig`. Expected either 'auto' or a string representing "
                    f"a `torch.dtype` (e.g., 'float32'), but got {torch_dtype}."
                )
            
            # Disable caching if gradient checkpointing is enabled (not supported)
            model_init_kwargs["use_cache"] = (
                False if args.gradient_checkpointing else model_init_kwargs.get("use_cache")
            )
            
            # Load ECG-Chat model using the builder
            print(f"Loading ECG-Chat model from {model_id}")
            tokenizer, model, image_processor, context_len = load_pretrained_model(
                model_path=model_id,
                model_base=None,  # Assuming merged model
                model_name="llava",
                device_map="auto",
                **model_init_kwargs
            )

            # Store the ECG tower for later use
            self.ecg_tower = model.get_ecg_tower() if hasattr(model, 'get_ecg_tower') else None
            print(f"ECG tower loaded: {self.ecg_tower is not None}")

            # Ensure ECG tower is properly loaded
            if self.ecg_tower and not self.ecg_tower.is_loaded:
                print("Loading ECG tower...")
                self.ecg_tower.load_model()
                print("ECG tower loaded successfully")
            
        else:
            model_id = model.config._name_or_path
            if args.model_init_kwargs is not None:
                raise ValueError(
                    "You passed `model_init_kwargs` to the `GRPOConfig`, but your model is already instantiated. "
                    "This argument can only be used when the `model` argument is a string."
                )
            tokenizer = None

        if peft_config is not None:
            model = get_peft_model(model, peft_config)

        # Reference model - create a copy for reference
        print("Creating reference model...")
        self.ref_model = create_reference_model(model, num_shared_layers=args.num_shared_layers)
        
        # Processing class (tokenizer)
        if processing_class is None:
            if tokenizer is None:
                processing_class = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
            else:
                processing_class = tokenizer
        
        # Ensure left padding for generation
        if processing_class.pad_token is None:
            processing_class.pad_token = processing_class.eos_token
        processing_class.padding_side = "left"
        
        self.processing_class = processing_class

        # Reward functions
        if not isinstance(reward_funcs, list):
            reward_funcs = [reward_funcs]
        self.reward_funcs = reward_funcs

        # Reward processing classes
        if reward_processing_classes is None:
            reward_processing_classes = [None] * len(reward_funcs)
        elif not isinstance(reward_processing_classes, list):
            reward_processing_classes = [reward_processing_classes]
        self.reward_processing_classes = reward_processing_classes

        # Initialize reward models
        self.reward_models = []
        for i, reward_func in enumerate(reward_funcs):
            if isinstance(reward_func, str):
                reward_model = AutoModelForSequenceClassification.from_pretrained(
                    reward_func, num_labels=1, trust_remote_code=True
                )
                self.reward_models.append(reward_model)
            elif isinstance(reward_func, PreTrainedModel):
                self.reward_models.append(reward_func)
            else:
                self.reward_models.append(None)  # Custom function

        # Generation config
        self.generation_config = GenerationConfig(
            max_new_tokens=args.max_completion_length,
            temperature=args.temperature,
            do_sample=True,
            pad_token_id=processing_class.pad_token_id,
            eos_token_id=processing_class.eos_token_id,
        )

        # GRPO specific parameters
        self.beta = args.beta
        self.num_generations = args.num_generations
        self.max_prompt_length = args.max_prompt_length
        self.max_completion_length = args.max_completion_length

        # Metrics tracking
        self._metrics = defaultdict(list)

        # Initialize parent Trainer
        super().__init__(
            model=model,
            args=args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=processing_class,
            callbacks=callbacks,
            optimizers=optimizers,
        )

        # Prepare models for distributed training
        if args.gradient_checkpointing:
            self.model.gradient_checkpointing_enable()

        if is_deepspeed_zero3_enabled():
            self.ref_model = prepare_deepspeed(self.ref_model, per_device_train_batch_size=args.per_device_train_batch_size, fp16=args.fp16, bf16=args.bf16)

    def preprocess_ecg(self, ecg_path, seq_length=None):
        """Preprocess ECG data for model input with enhanced error handling"""
        if seq_length is None:
            seq_length = self.seq_length

        try:
            # Handle empty or None paths
            if not ecg_path or ecg_path.strip() == "":
                print(f"Warning: Empty ECG path provided, using zero tensor")
                return torch.zeros((12, seq_length), dtype=torch.float32)

            # Handle both absolute and relative paths
            if not os.path.isabs(ecg_path) and self.ecg_folder:
                full_path = os.path.join(self.ecg_folder, ecg_path)
            else:
                full_path = ecg_path

            # Remove .hea extension if present
            if full_path.endswith('.hea'):
                full_path = full_path[:-4]

            # Check if file exists
            if not os.path.exists(full_path + '.hea') and not os.path.exists(full_path + '.dat'):
                print(f"Warning: ECG files not found at {full_path}, using zero tensor")
                return torch.zeros((12, seq_length), dtype=torch.float32)

            # Load ECG data using wfdb
            ecg_data, fields = wfdb.rdsamp(full_path)

            # Validate ECG data
            if ecg_data is None or ecg_data.size == 0:
                print(f"Warning: Empty ECG data from {full_path}, using zero tensor")
                return torch.zeros((12, seq_length), dtype=torch.float32)

            # Handle NaN and Inf values
            ecg_data = np.nan_to_num(ecg_data, nan=0.0, posinf=0.0, neginf=0.0)

            # Ensure we have the expected number of channels (12-lead ECG)
            if ecg_data.shape[1] != 12:
                print(f"Warning: Expected 12 channels, got {ecg_data.shape[1]} for {full_path}")
                # Pad or truncate channels
                if ecg_data.shape[1] < 12:
                    padded_data = np.zeros((ecg_data.shape[0], 12), dtype=ecg_data.dtype)
                    padded_data[:, :ecg_data.shape[1]] = ecg_data
                    ecg_data = padded_data
                else:
                    ecg_data = ecg_data[:, :12]

            # Transpose to (channels, length) format
            ecg_tensor = torch.Tensor(np.transpose(ecg_data, (1, 0)).astype(np.float32))

            # Pad or truncate to fixed length (consistent with training)
            c, length = ecg_tensor.shape
            if length < seq_length:
                new_ecg = torch.zeros((c, seq_length), dtype=ecg_tensor.dtype)
                new_ecg[:, 0:length] = ecg_tensor
                ecg_tensor = new_ecg
            elif length > seq_length:
                ecg_tensor = ecg_tensor[:, 0:seq_length]

            # Final validation
            if torch.isnan(ecg_tensor).any() or torch.isinf(ecg_tensor).any():
                print(f"Warning: NaN/Inf detected after preprocessing {full_path}, cleaning...")
                ecg_tensor = torch.nan_to_num(ecg_tensor, nan=0.0, posinf=0.0, neginf=0.0)

            return ecg_tensor

        except Exception as e:
            print(f"Error preprocessing ECG {ecg_path}: {e}")
            print(f"Using zero tensor as fallback")
            # Return zero tensor as fallback
            return torch.zeros((12, seq_length), dtype=torch.float32)

    def tokenize_ecg_prompt(self, prompt_text, return_tensors="pt"):
        """Tokenize prompt with ECG token handling"""
        return tokenizer_ecg_token(
            prompt_text,
            self.processing_class,
            ECG_TOKEN_INDEX=ECG_TOKEN_INDEX,
            return_tensors=return_tensors
        )

    def _get_per_token_logps_ecg(self, model, input_ids, attention_mask, ecgs):
        """Get per-token log probabilities for ECG multimodal model"""
        # Forward pass with ECG data
        logits = model(input_ids=input_ids, attention_mask=attention_mask, ecgs=ecgs).logits
        logits = logits[:, :-1, :]  # (B, L-1, V), exclude the last logit
        input_ids = input_ids[:, 1:]  # (B, L-1), exclude the first input ID

        # Compute the log probabilities for the input tokens
        per_token_logps = []
        for logits_row, input_ids_row in zip(logits, input_ids):
            log_probs = logits_row.log_softmax(dim=-1)
            token_log_prob = torch.gather(log_probs, dim=1, index=input_ids_row.unsqueeze(1)).squeeze(1)
            per_token_logps.append(token_log_prob)
        return torch.stack(per_token_logps)

    def _prepare_inputs(self, inputs: dict[str, Union[torch.Tensor, Any]]) -> dict[str, Union[torch.Tensor, Any]]:
        """Override to skip automatic tensor conversion since we handle it in compute_loss"""
        return inputs

    def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):
        """Compute GRPO loss for ECG-Chat model with enhanced logging"""
        if return_outputs:
            raise ValueError("The ECGChatGRPOTrainer does not support returning outputs")

        batch_size = len(inputs)
        print(f"Processing batch of size {batch_size}")

        # Extract prompts and ECG paths
        prompts = [x["prompt"] for x in inputs]
        ecg_paths = [x.get("ecg_path", x.get("ecg", "")) for x in inputs]

        # Log ECG paths for debugging
        if os.getenv("DEBUG_MODE") == "true":
            print(f"ECG paths in batch: {ecg_paths[:3]}...")  # Show first 3 paths

        # Process prompts to text format
        prompts_text = []
        for example in inputs:
            prompt_text = maybe_apply_chat_template(example, self.processing_class)["prompt"]
            # Ensure ECG token is present
            if DEFAULT_ECG_TOKEN not in prompt_text:
                prompt_text = f"{DEFAULT_ECG_TOKEN}\n{prompt_text}"
            prompts_text.append(prompt_text)

        # Tokenize prompts with ECG token handling
        prompt_inputs_list = []
        for prompt_text in prompts_text:
            tokenized = self.tokenize_ecg_prompt(prompt_text, return_tensors="pt")
            prompt_inputs_list.append(tokenized)

        # Pad and batch the tokenized inputs
        max_length = max(inp.size(1) for inp in prompt_inputs_list)
        prompt_ids_list = []
        prompt_mask_list = []

        for tokenized in prompt_inputs_list:
            seq_len = tokenized.size(1)
            if seq_len < max_length:
                # Left pad
                pad_length = max_length - seq_len
                padded_ids = torch.cat([
                    torch.full((1, pad_length), self.processing_class.pad_token_id, dtype=tokenized.dtype),
                    tokenized
                ], dim=1)
                padded_mask = torch.cat([
                    torch.zeros(1, pad_length, dtype=torch.bool),
                    torch.ones(1, seq_len, dtype=torch.bool)
                ], dim=1)
            else:
                padded_ids = tokenized
                padded_mask = torch.ones_like(tokenized, dtype=torch.bool)

            prompt_ids_list.append(padded_ids)
            prompt_mask_list.append(padded_mask)

        prompt_ids = torch.cat(prompt_ids_list, dim=0)
        prompt_mask = torch.cat(prompt_mask_list, dim=0)

        # Preprocess ECG data with memory optimization
        ecgs_list = []
        failed_ecg_count = 0

        for i, ecg_path in enumerate(ecg_paths):
            try:
                if ecg_path:
                    ecg_tensor = self.preprocess_ecg(ecg_path)
                    ecgs_list.append(ecg_tensor.unsqueeze(0))
                else:
                    # Create dummy ECG tensor if no path provided
                    ecgs_list.append(torch.zeros(1, 12, self.seq_length))
                    failed_ecg_count += 1
            except Exception as e:
                print(f"Failed to process ECG {i}: {ecg_path}, error: {e}")
                ecgs_list.append(torch.zeros(1, 12, self.seq_length))
                failed_ecg_count += 1

        if failed_ecg_count > 0:
            print(f"Warning: {failed_ecg_count}/{len(ecg_paths)} ECG files failed to load")

        ecgs = torch.cat(ecgs_list, dim=0)

        # Clear intermediate tensors to save memory
        del ecgs_list

        # Move to device
        prompt_ids = prompt_ids.to(self.accelerator.device)
        prompt_mask = prompt_mask.to(self.accelerator.device)
        ecgs = ecgs.to(self.accelerator.device)

        # Truncate if necessary
        if self.max_prompt_length is not None:
            prompt_ids = prompt_ids[:, -self.max_prompt_length:]
            prompt_mask = prompt_mask[:, -self.max_prompt_length:]

        # Generate completions with error handling
        try:
            print(f"Starting generation with prompt length {prompt_ids.size(1)}")
            with unwrap_model_for_generation(model, self.accelerator) as unwrapped_model:
                prompt_completion_ids = unwrapped_model.generate(
                    input_ids=prompt_ids,
                    attention_mask=prompt_mask,
                    ecgs=ecgs,
                    generation_config=self.generation_config
                )

                prompt_length = prompt_ids.size(1)
                completion_ids = prompt_completion_ids[:, prompt_length:]
                prompt_mask = prompt_mask.repeat_interleave(self.num_generations, dim=0)

                print(f"Generation completed, completion length: {completion_ids.size(1)}")

        except Exception as e:
            print(f"Error during generation: {e}")
            # Create dummy completions as fallback
            completion_ids = torch.full(
                (prompt_ids.size(0) * self.num_generations, 10),
                self.processing_class.eos_token_id,
                device=prompt_ids.device
            )
            prompt_mask = prompt_mask.repeat_interleave(self.num_generations, dim=0)

        # Mask everything after the first EOS token
        is_eos = completion_ids == self.processing_class.eos_token_id
        device = self.accelerator.device
        eos_idx = torch.full((is_eos.size(0),), is_eos.size(1), dtype=torch.long, device=device)
        eos_idx[is_eos.any(dim=1)] = is_eos.int().argmax(dim=1)[is_eos.any(dim=1)]

        completion_mask = torch.arange(completion_ids.size(1), device=device).unsqueeze(0) < eos_idx.unsqueeze(1)

        # Prepare inputs for log probability computation
        prompt_completion_ids = torch.cat([prompt_ids.repeat_interleave(self.num_generations, dim=0), completion_ids], dim=1)
        ecgs_repeated = ecgs.repeat_interleave(self.num_generations, dim=0)
        attention_mask_full = torch.cat([prompt_mask, completion_mask], dim=1)

        # Compute per-token log probabilities for policy model
        per_token_logps = self._get_per_token_logps_ecg(model, prompt_completion_ids, attention_mask_full, ecgs_repeated)
        per_token_logps = per_token_logps[:, prompt_length - 1:]

        # Compute per-token log probabilities for reference model
        with torch.no_grad():
            ref_per_token_logps = self._get_per_token_logps_ecg(self.ref_model, prompt_completion_ids, attention_mask_full, ecgs_repeated)
            ref_per_token_logps = ref_per_token_logps[:, prompt_length - 1:]

        # Compute KL divergence
        per_token_kl = torch.exp(ref_per_token_logps - per_token_logps) - (ref_per_token_logps - per_token_logps) - 1

        # Decode completions for reward computation
        completions = self.processing_class.batch_decode(completion_ids, skip_special_tokens=True)
        if is_conversational(inputs[0]):
            completions = [[{"role": "assistant", "content": completion}] for completion in completions]

        # Compute rewards
        prompts_repeated = [prompt for prompt in prompts for _ in range(self.num_generations)]
        rewards = self._compute_rewards(prompts_repeated, completions, inputs)

        # Reshape rewards and compute advantages
        rewards = torch.tensor(rewards, dtype=torch.float, device=device).view(-1, self.num_generations)

        # Group-wise advantage normalization
        advantages = rewards - rewards.mean(dim=1, keepdim=True)
        advantages = advantages / (rewards.std(dim=1, keepdim=True) + 1e-8)
        advantages = advantages.view(-1)

        # Compute GRPO loss
        per_token_loss = torch.exp(per_token_logps - per_token_logps.detach()) * advantages.unsqueeze(1)
        per_token_loss = -(per_token_loss - self.beta * per_token_kl)
        loss = ((per_token_loss * completion_mask).sum(dim=1) / completion_mask.sum(dim=1)).mean()

        # Log metrics
        completion_length = self.accelerator.gather_for_metrics(completion_mask.sum(1)).float().mean().item()
        self._metrics["completion_length"].append(completion_length)

        mean_reward = self.accelerator.gather_for_metrics(rewards.mean(dim=1)).mean().item()
        self._metrics["reward"].append(mean_reward)

        mean_kl = self.accelerator.gather_for_metrics((per_token_kl * completion_mask).sum(dim=1) / completion_mask.sum(dim=1)).mean().item()
        self._metrics["kl"].append(mean_kl)

        return loss

    def _compute_rewards(self, prompts, completions, inputs):
        """Compute rewards for the generated completions"""
        rewards = []

        for i, reward_func in enumerate(self.reward_funcs):
            if isinstance(reward_func, str) or isinstance(reward_func, PreTrainedModel):
                # Use model-based reward
                reward_model = self.reward_models[i]
                reward_processing_class = self.reward_processing_classes[i]

                if reward_processing_class is None:
                    if isinstance(reward_func, str):
                        reward_processing_class = AutoTokenizer.from_pretrained(reward_func, trust_remote_code=True)
                    else:
                        reward_processing_class = self.processing_class

                # Prepare inputs for reward model
                reward_inputs = []
                for prompt, completion in zip(prompts, completions):
                    if is_conversational(prompt):
                        # Convert to text format
                        prompt_text = self.processing_class.apply_chat_template(prompt, tokenize=False)
                        completion_text = completion[0]["content"] if isinstance(completion, list) else completion
                    else:
                        prompt_text = prompt
                        completion_text = completion

                    combined_text = f"{prompt_text}\n{completion_text}"
                    reward_inputs.append(combined_text)

                # Tokenize and compute rewards
                reward_tokenized = reward_processing_class(
                    reward_inputs,
                    return_tensors="pt",
                    padding=True,
                    truncation=True,
                    max_length=512
                ).to(self.accelerator.device)

                with torch.no_grad():
                    reward_outputs = reward_model(**reward_tokenized)
                    batch_rewards = reward_outputs.logits.squeeze(-1).cpu().tolist()

                if i == 0:
                    rewards = batch_rewards
                else:
                    rewards = [r1 + r2 for r1, r2 in zip(rewards, batch_rewards)]

            else:
                # Use custom reward function
                batch_rewards = reward_func(prompts, completions, inputs)
                if i == 0:
                    rewards = batch_rewards
                else:
                    rewards = [r1 + r2 for r1, r2 in zip(rewards, batch_rewards)]

        return rewards

    def log(self, logs: dict[str, float]) -> None:
        """Log metrics including GRPO-specific ones"""
        # Add GRPO metrics to logs
        for key, values in self._metrics.items():
            if values:
                logs[f"train/{key}"] = sum(values) / len(values)

        # Clear metrics after logging
        self._metrics.clear()

        super().log(logs)

    def push_to_hub(self, dataset_name: str = None, **kwargs) -> str:
        """Push model to hub with ECG-Chat specific model card"""
        # Generate model card
        base_model = self.model.config._name_or_path if hasattr(self.model.config, '_name_or_path') else "ECG-Chat"
        model_name = f"{base_model}-GRPO"

        tags = ["ecg", "multimodal", "grpo", "reinforcement-learning", "medical-ai"]

        citation = textwrap.dedent("""\
            @article{yuan2024deepseekmath,
                title={DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models},
                author={Zhihong Shao and Peiyi Wang and Qihao Zhu and Runxin Xu and Junxiao Song and Mingchuan Zhang and Y.K. Li and Y. Wu and Daya Guo},
                journal={arXiv preprint arXiv:2402.03300},
                year={2024}
            }
        """)

        model_card = generate_model_card(
            base_model=base_model,
            model_name=model_name,
            hub_model_id=self.hub_model_id,
            dataset_name=dataset_name,
            tags=tags,
            wandb_url=wandb.run.get_url() if is_wandb_available() and wandb.run is not None else None,
            comet_url=get_comet_experiment_url(),
            trainer_name="ECG-GRPO",
            trainer_citation=citation,
            paper_title="DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models",
            paper_id="2402.03300",
        )

        model_card.save(os.path.join(self.args.output_dir, "README.md"))

        return super().push_to_hub(**kwargs)
