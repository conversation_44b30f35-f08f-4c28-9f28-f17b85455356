# ECG-Chat GRPO Training Guide

This guide explains how to use the ECG-Chat GRPO trainer for reinforcement learning on ECG multimodal tasks.

## Prerequisites

1. **ECG-Chat Model**: You need a trained ECG-Chat model (preferably with LoRA weights merged)
2. **ECG Data**: ECG data in WFDB format (.dat, .hea files)
3. **GRPO Dataset**: A dataset formatted for GRPO training with ECG tasks

## Dataset Format

The GRPO dataset should be in HuggingFace datasets format with the following structure:

```json
{
    "prompt": [
        {
            "role": "user", 
            "content": "What abnormalities do you see in this ECG?"
        }
    ],
    "ecg_path": "relative/path/to/ecg/file",
    "solution": "The ECG shows atrial fibrillation with rapid ventricular response."
}
```

### Key Fields:
- `prompt`: Conversational format prompt (will be automatically prefixed with `<ecg>` token)
- `ecg_path`: Relative path to ECG file (without .hea extension)
- `solution`: Ground truth answer for reward computation

## Configuration

### 1. Model Paths
```bash
MODEL_PATH="/path/to/ecg-chat/merged/model"    # Merged ECG-Chat model
ECG_TOWER_PATH="/path/to/ecg/tower"            # ECG encoder tower
ECG_FOLDER="/path/to/ecg/data"                 # Root folder for ECG files
```

### 2. Training Parameters
```bash
NUM_GENERATIONS=4          # Number of completions per prompt
MAX_PROMPT_LENGTH=512      # Maximum input length
MAX_COMPLETION_LENGTH=256  # Maximum generation length
TEMPERATURE=1.0            # Sampling temperature
BETA=0.1                   # GRPO regularization parameter
```

### 3. Reward Functions
Available reward functions:
- `ecg_accuracy`: Medical accuracy based on term matching
- `ecg_format`: Response format and length validation
- `accuracy`: General accuracy (exact match)
- `format`: General format validation

## Usage

### 1. Prepare Your Data
Ensure your ECG data is in WFDB format and your dataset follows the required structure.

### 2. Configure the Script
Edit `run_grpo_ecgchat.sh` and set the correct paths:

```bash
# Update these paths
MODEL_PATH="/your/path/to/ecg-chat/model"
ECG_TOWER_PATH="/your/path/to/ecg/tower"
ECG_FOLDER="/your/path/to/ecg/data"
DATASET_NAME="/your/path/to/grpo/dataset"
OUTPUT_DIR="/your/path/to/output"
```

### 3. Run Training
```bash
cd /data/yuanxiaoyan/workspace/R1-V-main/src/r1-v
./run_grpo_ecgchat.sh
```

## Advanced Configuration

### Custom Reward Functions
You can add custom reward functions in `grpo.py`:

```python
def custom_ecg_reward(completions, solution, **kwargs):
    # Your custom reward logic here
    rewards = []
    for completion, sol in zip(completions, solution):
        # Compute reward based on your criteria
        reward = compute_reward(completion, sol)
        rewards.append(reward)
    return rewards

# Register the function
reward_funcs_registry["custom_ecg"] = custom_ecg_reward
```

### Multi-GPU Training
The script supports multi-GPU training via torchrun. Adjust `--nproc_per_node` based on your setup:

```bash
torchrun --nproc_per_node="8"  # For 8 GPUs
```

### Memory Optimization
For large models or limited memory:
- Reduce `per_device_train_batch_size`
- Increase `gradient_accumulation_steps`
- Enable `gradient_checkpointing`
- Use `bf16` precision

## Monitoring

### Weights & Biases
The script automatically logs to W&B. Key metrics include:
- `train/reward`: Average reward per batch
- `train/kl`: KL divergence from reference model
- `train/completion_length`: Average completion length

### Debug Mode
Enable debug logging:
```bash
export DEBUG_MODE="true"
export LOG_PATH="./debug_log_ecg_grpo.txt"
```

## Troubleshooting

### Common Issues

1. **ECG Loading Errors**
   - Ensure ECG files are in WFDB format
   - Check that `ecg_folder` path is correct
   - Verify file permissions

2. **Model Loading Errors**
   - Ensure ECG-Chat model is properly merged (if using LoRA)
   - Check that ECG tower path is correct
   - Verify model compatibility

3. **Memory Issues**
   - Reduce batch size
   - Enable gradient checkpointing
   - Use smaller sequence lengths

4. **Reward Computation Issues**
   - Check dataset format
   - Verify solution field exists
   - Test reward functions separately

## Example Dataset Creation

```python
from datasets import Dataset

# Create sample data
data = [
    {
        "prompt": [{"role": "user", "content": "Analyze this ECG for abnormalities."}],
        "ecg_path": "patient001/ecg_001",
        "solution": "Normal sinus rhythm with no significant abnormalities."
    },
    # Add more samples...
]

# Create dataset
dataset = Dataset.from_list(data)
dataset.save_to_disk("/path/to/ecg/grpo/dataset")
```

## Performance Tips

1. **Data Loading**: Use multiple workers for faster data loading
2. **ECG Preprocessing**: Consider pre-processing ECG data to reduce runtime overhead
3. **Batch Size**: Find optimal batch size for your hardware
4. **Checkpointing**: Save frequently to avoid losing progress

For more details, see the ECG-Chat and R1-V documentation.
