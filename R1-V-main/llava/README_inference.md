# LLaVA ECG Inference Scripts

This directory contains scripts for running inference with the fine-tuned LLaVA model on ECG data.

## Files

- `llm_inference_lora.sh` - Bash script for simple inference
- `llm_inference_lora.py` - Python script with full functionality
- `example_usage.py` - Usage examples
- `README_inference.md` - This documentation

## Prerequisites

1. **Model Files**: Ensure the following files exist:
   - LoRA model: `/data/yuanxiaoyan/ECGpro/ECG-Chat/checkpoints/llava-vicuna-13b-v1.5-finetune_lora`
   - Base model: `/data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5`
   - ECG tower: `/data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt`
   - ECG data: `/data/yuanxiaoyan/ECGpro/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0`

2. **Dependencies**: Install required packages:
   ```bash
   pip install torch transformers wfdb numpy
   ```

## Data Format

### ECG Data
- **Format**: WFDB format (.dat, .hea files)
- **Channels**: 12-lead ECG
- **Sequence Length**: Fixed to 5000 samples
- **Preprocessing**: Automatic padding/truncation, NaN/Inf handling

### Input JSON Format
```json
[
    {
        "id": "sample_id",
        "ecg": "relative/path/to/ecg/file",
        "conversations": [
            {
                "from": "human",
                "value": "<ecg>\nYour question about the ECG"
            },
            {
                "from": "gpt", 
                "value": ""
            }
        ]
    }
]
```

## Usage

### 1. Bash Script (Simple)

```bash
# Make executable
chmod +x llm_inference_lora.sh

# Run inference
./llm_inference_lora.sh "files/p1000/p10000032/s40689238/40689238" "Is there any abnormality?"

# With default question
./llm_inference_lora.sh "files/p1000/p10000032/s40689238/40689238"
```

### 2. Python Script (Full Features)

#### Single File Inference
```bash
python llm_inference_lora.py --ecg_file "files/p1000/p10000032/s40689238/40689238" --question "What is the heart rate?"
```

#### Interactive Mode
```bash
python llm_inference_lora.py --interactive
```

#### Batch Processing
```bash
python llm_inference_lora.py --batch_input input.json --batch_output results.json
```

#### Custom Model Paths
```bash
python llm_inference_lora.py \
    --model_path /path/to/lora/model \
    --base_model_path /path/to/base/model \
    --ecg_tower_path /path/to/ecg/tower \
    --ecg_folder /path/to/ecg/data \
    --ecg_file "ecg_file_path"
```

### 3. Programmatic Usage

```python
from llm_inference_lora import LLaVAECGInference

# Initialize
inference = LLaVAECGInference(
    model_path='/path/to/lora/model',
    base_model_path='/path/to/base/model',
    ecg_tower_path='/path/to/ecg/tower',
    ecg_folder='/path/to/ecg/data'
)

# Load model
inference.load_model()

# Single inference
response = inference.generate_response(
    ecg_path='files/p1000/p10000032/s40689238/40689238',
    question='Is there any abnormality?'
)

# Batch inference
inference.batch_inference('input.json', 'output.json')
```

## Parameters

### Model Parameters
- `--model_path`: Path to LoRA fine-tuned model
- `--base_model_path`: Path to base Vicuna model
- `--ecg_tower_path`: Path to ECG encoder weights
- `--ecg_folder`: Directory containing ECG data files

### Inference Parameters
- `--ecg_file`: ECG file path (relative to ECG folder)
- `--question`: Question for ECG analysis
- `--interactive`: Run in interactive mode
- `--batch_input`: Input JSON file for batch processing
- `--batch_output`: Output JSON file for batch results
- `--device`: Device to use (cuda/cpu)

### Generation Parameters (in Python)
- `max_new_tokens`: Maximum tokens to generate (default: 512)
- `temperature`: Sampling temperature (default: 0.7)
- `top_p`: Nucleus sampling parameter (default: 0.9)

## Output Format

### Single Inference
The script outputs the model's response to the console.

### Batch Inference
Results are saved as JSON:
```json
[
    {
        "id": "sample_id",
        "ecg": "ecg_file_path",
        "question": "question_text",
        "response": "model_response"
    }
]
```

## Examples

### Example 1: Basic Analysis
```bash
python llm_inference_lora.py \
    --ecg_file "files/p1000/p10000032/s40689238/40689238" \
    --question "Is there any abnormality in the electrocardiogram?"
```

### Example 2: Heart Rate Analysis
```bash
python llm_inference_lora.py \
    --ecg_file "files/p1000/p10000032/s40689238/40689238" \
    --question "What is the heart rate and rhythm pattern?"
```

### Example 3: Interactive Session
```bash
python llm_inference_lora.py --interactive
# Then follow prompts to enter ECG files and questions
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size or use CPU: `--device cpu`
   - Use 8-bit quantization (modify script)

2. **Model Not Found**
   - Check model paths are correct
   - Ensure all required files exist

3. **ECG File Not Found**
   - Verify ECG file path is relative to ECG folder
   - Check file exists in the specified location

4. **Import Errors**
   - Ensure PYTHONPATH includes the llava directory
   - Install missing dependencies

### Performance Tips

1. **GPU Memory**: Use `torch.cuda.empty_cache()` between inferences
2. **Batch Processing**: Process multiple files together for efficiency
3. **Model Loading**: Load model once and reuse for multiple inferences

## Notes

- The model expects ECG data in WFDB format
- ECG sequences are automatically padded/truncated to 5000 samples
- The model uses v1 conversation template
- LoRA weights are automatically loaded and merged
