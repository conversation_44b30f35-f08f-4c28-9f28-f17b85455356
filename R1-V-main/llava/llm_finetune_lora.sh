#!/bin/bash

script_dir=$(dirname "$(realpath "$0")")
open_clip_dir="$script_dir/../open_clip"
export PYTHONPATH="$script_dir:$open_clip_dir:$PYTHONPATH"

MODEL_VERSION="vicuna-13b-v1.5"

WANDB_MODE=offline deepspeed --include "localhost:1,2,4,5" /data/yuanxiaoyan/ECGpro/ECG-Chat/llava/llava/train/train_mem.py \
    --deepspeed "$script_dir/llava/scripts/zero2.json" \
    --lora_enable True \
    --model_name_or_path /data/yuanxiaoyan/ECGpro/Pertrain_M/lmsys/vicuna-13b-v1.5 \
    --version v1 \
    --data_path /data/yuanxiaoyan/ECGpro/ECG-Chat/llava/playground/data/ecg_instruct_45k.json \
    --ecg_folder /data/yuanxiaoyan/ECGpro/mimic-iv-ecg-diagnostic-electrocardiogram-matched-subset-1.0 \
    --ecg_tower /data/yuanxiaoyan/ECGpro/cpt_wfep_epoch_20.pt \
    --open_clip_config coca_ViT-B-32 \
    --pretrain_mm_mlp_adapter ./checkpoints/llava-$MODEL_VERSION-pretrain/mm_projector.bin \
    --mm_projector_type linear \
    --mm_use_ecg_start_end False \
    --mm_use_ecg_patch_token False \
    --group_by_modality_length True \
    --bf16 True \
    --output_dir ./checkpoints/llava-$MODEL_VERSION-finetune-ecginstruct_lora \
    --num_train_epochs 3 \
    --per_device_train_batch_size 16 \
    --per_device_eval_batch_size 4 \
    --gradient_accumulation_steps 1 \
    --evaluation_strategy "no" \
    --save_strategy "steps" \
    --save_steps 1000 \
    --save_total_limit 5 \
    --learning_rate 2e-4 \
    --weight_decay 0. \
    --warmup_ratio 0.03 \
    --lr_scheduler_type "cosine" \
    --logging_steps 1 \
    --tf32 True \
    --model_max_length 2048 \
    --gradient_checkpointing True \
    --dataloader_num_workers 4 \
    --lazy_preprocess True