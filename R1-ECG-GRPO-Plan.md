### R1-Omni vs R1-V 差异与 ECG-Chat 迁移到 GRPO 的实施要点

- **R1-V 是原始代码；R1-Omni 在其上做了 Human-Omni 的适配修改。**

## 1) 关键差异（R1-Omni 相对 R1-V）
- **Trainer 扩展到新模态**
  - 在 `r1-v/src/open_r1/trainer/grpo_trainer.py` 中加入视频分支：
    - 依赖 `qwen_vl_utils.process_vision_info`；按 `image`/`video` 分路处理；新增 `_get_per_token_logps_video(...)`。
    - 生成时为视频按时长动态设 `fps`，并在 prompt 中注入视频占位。
  - 新增自定义多模态 Trainer：`humanOmni_grpo_trainer.py`：
    - 加载 Human-Omni 自有模型、视觉/音频塔与处理器（SigLIP/Whisper/BERT）。
    - 构造多模态输入（视频、音频、文本），仅对“答案 token”切片计算 logp，保持 GRPO 的 KL 与优势归一化逻辑不变。
- **vLLM 训练器**
  - 双方均有 `vllm_grpo_trainer.py`；R1-V 在 `grpo.py` 下 `--use_vllm` 会选 modified 版本。
  - R1-Omni 主流程更多依赖自定义 Trainer 以支持视频/音频。
- **入口与运行脚本**
  - R1-V：`scripts/run_grpo_vllm.sh` 走标准 Qwen2-VL + vLLM。
  - R1-Omni：新增 `r1-v/run_grpo_humanomni.sh`，指向人类情感多模态权重/数据，`num_generations=8` 等。
- **数据与提示格式**
  - R1-V：图像任务格式为 `{"content": [{"type":"image"},{"type":"text"}]}`。
  - R1-Omni：对视频/音频使用占位与特殊标记（如 `<video>`、`<audio>` 标记）。
- **处理器与 pad/eos 设置**
  - R1-V：`AutoProcessor/AutoTokenizer`，为 Qwen2-VL 设置 `max_pixels/min_pixels` 与左侧 pad。
  - R1-Omni：视觉/音频塔用专用处理器，文本 tokenizer 额外使用 BERT，并在 forward 使用自定义键名。

## 2) ECG-Chat SFT 关键信息（用于对齐 GRPO）
- SFT 启动脚本：`/data/yuanxiaoyan/ECGpro/ECG-Chat/llava/llm_finetune_lora.sh`。
  - 基座：Vicuna/LLaVA 风格；ECG 塔：`--ecg_tower`，open_clip 配置：`--open_clip_config`。
  - 数据：`ecg_instruct_45k.json` + `--ecg_folder` 指向原始 ECG 数据根目录。
- 推理/处理要点（代码位于 `llava/llm_inference_lora.py`、`llava/llava/mm_utils.py`）：
  - 提示需以 `<ecg>\n{question}` 开头；
  - 分词需与训练一致：`tokenizer_ecg_token(prompt, tokenizer, return_tensors='pt')`，在 `<ecg>` 处插入 `ECG_TOKEN_INDEX`；
  - 前向/生成签名：`model.generate(input_ids, ecgs=tensor, ...)` 与 `model(input_ids, ecgs=tensor, ...)`；
  - ECG 读取：使用 wfdb 读 `.hea` 同名文件；(C,L) 转置，处理 NaN/Inf，pad/trunc 至 `seq_length=5000`。

## 3) 在当前项目实现 ECG-Chat 的 GRPO（仅改本项目）
- 新增 Trainer：`r1-v/src/open_r1/trainer/ecg_grpo_trainer.py`
  - 加载策略模型与参考模型：
    - 策略模型：加载 ECG-Chat 的 SFT 权重（若为 LoRA，需要在本 Trainer 内合并非 LoRA权重与 LoRA，再训练）。
    - 参考模型：同构同权重（或按 R1-V 的 PEFT/disable_adapter 逻辑创建 ref）。
  - 对齐分词与提示：
    - 实现本地版 `tokenizer_ecg_token`（逻辑与 ECG-Chat 相同，使用相同 `ECG_TOKEN_INDEX`）。
    - 将样本问题映射为 `<ecg>\n{question}` 并分词到 `input_ids`（左侧 pad、设置 `pad_token_id/eos_token_id`）。
  - ECG 预处理：
    - 在 Trainer 内实现 `preprocess_ecg(path, seq_length=5000)`，批量读取为张量；
    - 训练时将 `ecgs=batch_tensor` 传入模型 forward 与 generate。
  - 生成与对数似然：
    - 使用 `unwrap_model_for_generation(...).generate(input_ids, ecgs=...)` 获取补全；
    - 计算策略/参考模型对 completion 部分的逐 token logp，构造 KL、组内归一化优势与 GRPO 损失；
    - 指标记录沿用 R1-V（奖励、KL、completion 长度等）。

- 更新入口：`r1-v/src/open_r1/grpo.py`
  - 增加选择逻辑（如 `--modality ecg` 或按 `model_name_or_path`/配置判断）以使用 `ECGChatGRPOTrainer`。
  - 数据映射：
    - 从 HF/JSON 读取样本，产出：
      - 文本 prompt：`{"prompt": [{"role":"user","content": "<ecg>\n{question}"}]}`；
      - ECG 路径字段（如 `ecg_path`），供 Trainer 在 `compute_loss` 内批量读取处理；
    - 或在数据预处理阶段直接将 ECG 转为张量字段传给 Trainer。

- 新增运行脚本：`r1-v/run_grpo_ecgchat.sh`
  - 指定：
    - `--model_name_or_path`：ECG-Chat SFT（合并 LoRA 后）权重目录；
    - 数据文件/集路径与 ECG 根目录；
    - GRPO 超参（`num_generations`、`max_prompt_length`、`max_completion_length`、`bf16`、`gradient_checkpointing`、保存频率等）；
  - 首版不启用 vLLM（多模态 ECG 自定义接口通常不受 vLLM 开箱支持）。

## 4) 风险与核对清单
- 分词一致性：`<ecg>` 的 token 注入与 `ECG_TOKEN_INDEX` 必须与 SFT 一致。
- 模型签名一致性：forward/generate 均需传 `ecgs=`；ref 模型也需同构同签名。
- ECG 长度与采样：保持训练时的 `seq_length=5000` 与通道顺序 (C,L)。
- 奖励函数：可先用 R1-V 提供的 `accuracy/format`；ECG 任务可后续替换为任务特定评分（标签/数值/序列一致性等）。

## 5) 关键文件定位（参考）
- R1-V 原始：`r1-v/src/open_r1/trainer/grpo_trainer.py`、`r1-v/src/open_r1/grpo.py`、`scripts/run_grpo_vllm.sh`
- R1-Omni 扩展：`r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py`、`r1-v/run_grpo_humanomni.sh`
- ECG-Chat（只读参考，不修改）：
  - SFT 脚本：`/data/yuanxiaoyan/ECGpro/ECG-Chat/llava/llm_finetune_lora.sh`
  - 分词与常量：`llava/llava/mm_utils.py`（`tokenizer_ecg_token`、`ECG_TOKEN_INDEX`）
  - 推理与前向签名示例：`llava/llm_inference_lora.py`（`generate(..., ecgs=...)`）
