# Model arguments
model_name_or_path: Qwen/Qwen2-VL-2B-Instruct
model_revision: main
torch_dtype: bfloat16

# Data training arguments
dataset_name: MMInstruction/Clevr_CoGenT_TrainA_R1
dataset_configs:
- all
preprocessing_num_workers: 8

# SFT trainer config
bf16: true
do_eval: true
eval_strategy: "no"
gradient_accumulation_steps: 4
gradient_checkpointing: true
gradient_checkpointing_kwargs:
  use_reentrant: false
hub_model_id: Qwen2-VL-2B-Instruct-SFT
hub_strategy: every_save
learning_rate: 2.0e-05
log_level: info
logging_steps: 5
logging_strategy: steps
lr_scheduler_type: cosine
packing: true
max_seq_length: 4096
max_steps: -1
num_train_epochs: 1
output_dir: data/Qwen2-VL-2B-Instruct-SFT
overwrite_output_dir: true
per_device_eval_batch_size: 4
per_device_train_batch_size: 4
push_to_hub: true
report_to:
- wandb
save_strategy: "no"
seed: 42
warmup_ratio: 0.1